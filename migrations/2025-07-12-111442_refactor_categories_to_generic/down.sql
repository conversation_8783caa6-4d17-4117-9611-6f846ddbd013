-- Rollback Categories Refactor
-- Restore laptop_categories table and migrate data back

-- ===== RECREATE laptop_categories TABLE =====

CREATE TABLE laptop_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    slug VARCHAR(60) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== MIGRATE DATA BACK FROM categories TO laptop_categories =====

INSERT INTO laptop_categories (
    id, name, slug, description, is_active,
    created_by, updated_by, created_at, updated_at
)
SELECT
    id, name, slug, description, is_active,
    created_by, updated_by, created_at, updated_at
FROM categories
WHERE category_type = 'laptops';

-- ===== UPDATE FOREIGN KEY REFERENCES BACK =====

-- Add temporary column to laptops table
ALTER TABLE laptops ADD COLUMN old_category_id UUID;

-- Update old_category_id to point to laptop_categories table
UPDATE laptops
SET old_category_id = category_id;

-- Add foreign key constraint to old column
ALTER TABLE laptops
ADD CONSTRAINT laptops_category_id_fkey
FOREIGN KEY (old_category_id) REFERENCES laptop_categories(id);

-- Drop new foreign key constraint
ALTER TABLE laptops DROP CONSTRAINT fk_laptops_categories;

-- Drop new category_id column
ALTER TABLE laptops DROP COLUMN category_id;

-- Rename old_category_id to category_id
ALTER TABLE laptops RENAME COLUMN old_category_id TO category_id;

-- Make category_id NOT NULL
ALTER TABLE laptops ALTER COLUMN category_id SET NOT NULL;

-- ===== RECREATE OLD INDEXES =====

CREATE INDEX idx_laptop_categories_slug ON laptop_categories(slug);
CREATE INDEX idx_laptop_categories_active ON laptop_categories(is_active);
CREATE INDEX idx_laptop_categories_created_by ON laptop_categories(created_by);

-- ===== RECREATE OLD TRIGGER =====

CREATE TRIGGER update_laptop_categories_updated_at
    BEFORE UPDATE ON laptop_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===== DROP NEW categories TABLE =====

-- Drop trigger first
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;

-- Drop indexes
DROP INDEX IF EXISTS idx_categories_created_by;
DROP INDEX IF EXISTS idx_categories_name_type;
DROP INDEX IF EXISTS idx_categories_slug_type;
DROP INDEX IF EXISTS idx_categories_active_type;
DROP INDEX IF EXISTS idx_categories_type;

-- Drop the new table
DROP TABLE categories;
