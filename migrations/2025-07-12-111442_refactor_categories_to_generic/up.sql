-- Refactor Categories to Generic Design
-- Migrate from laptop_categories to generic categories table
-- Support multiple business domains (laptops, phones, cars, etc.)

-- ===== CREATE NEW GENERIC CATEGORIES TABLE =====

CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(60) NOT NULL,
    description TEXT,
    category_type VARCHAR(20) NOT NULL, -- 'laptops', 'phones', 'cars', etc.
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Unique constraints per category type (allows same name/slug across different types)
    CONSTRAINT unique_name_per_type UNIQUE(name, category_type),
    CONSTRAINT unique_slug_per_type UNIQUE(slug, category_type)
);

-- ===== MIGRATE DATA FROM laptop_categories TO categories =====

INSERT INTO categories (
    id, name, slug, description, category_type, is_active,
    created_by, updated_by, created_at, updated_at
)
SELECT
    id, name, slug, description, 'laptops' as category_type, is_active,
    created_by, updated_by, created_at, updated_at
FROM laptop_categories;

-- ===== UPDATE FOREIGN KEY REFERENCES =====

-- Add temporary column to laptops table
ALTER TABLE laptops ADD COLUMN new_category_id UUID;

-- Update new_category_id to point to categories table
UPDATE laptops
SET new_category_id = category_id;

-- Add foreign key constraint to new column
ALTER TABLE laptops
ADD CONSTRAINT fk_laptops_categories
FOREIGN KEY (new_category_id) REFERENCES categories(id);

-- Drop old foreign key constraint
ALTER TABLE laptops DROP CONSTRAINT laptops_category_id_fkey;

-- Drop old category_id column
ALTER TABLE laptops DROP COLUMN category_id;

-- Rename new_category_id to category_id
ALTER TABLE laptops RENAME COLUMN new_category_id TO category_id;

-- Make category_id NOT NULL
ALTER TABLE laptops ALTER COLUMN category_id SET NOT NULL;

-- ===== CREATE INDEXES FOR PERFORMANCE =====

-- Categories indexes
CREATE INDEX idx_categories_type ON categories(category_type);
CREATE INDEX idx_categories_active_type ON categories(is_active, category_type);
CREATE INDEX idx_categories_slug_type ON categories(slug, category_type);
CREATE INDEX idx_categories_name_type ON categories(name, category_type);
CREATE INDEX idx_categories_created_by ON categories(created_by);

-- ===== CREATE TRIGGER FOR UPDATED_AT =====

CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===== DROP OLD laptop_categories TABLE =====

-- Drop indexes first
DROP INDEX IF EXISTS idx_laptop_categories_slug;
DROP INDEX IF EXISTS idx_laptop_categories_active;
DROP INDEX IF EXISTS idx_laptop_categories_created_by;

-- Drop trigger
DROP TRIGGER IF EXISTS update_laptop_categories_updated_at ON laptop_categories;

-- Drop the old table
DROP TABLE laptop_categories;
