-- Add Laptop Prices Table
-- Completes the laptop system with price management and user tracking

-- ===== LAPTOP PRICES TABLE =====

CREATE TABLE laptop_prices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,

    -- Price range thay vì giá cố định
    min_price DECIMAL(12,2) NOT NULL, -- <PERSON><PERSON><PERSON> thấp nhất
    max_price DECIMAL(12,2) NOT NULL, -- G<PERSON><PERSON> cao nhất
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',

    -- Metadata
    source VARCHAR(100), -- <PERSON><PERSON><PERSON><PERSON> giá (Amazon, Best Buy, v.v.)
    region VARCHAR(20) NOT NULL DEFAULT 'Global',
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,

    -- User tracking
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT check_price_range CHECK (max_price >= min_price)
);

-- ===== INDEXES FOR PERFORMANCE =====

-- Laptop prices indexes
CREATE INDEX idx_laptop_prices_laptop_current ON laptop_prices(laptop_id, is_current);
CREATE INDEX idx_laptop_prices_range ON laptop_prices(min_price, max_price);
CREATE INDEX idx_laptop_prices_region ON laptop_prices(region);
CREATE INDEX idx_laptop_prices_created_by ON laptop_prices(created_by);
CREATE INDEX idx_laptop_prices_effective_date ON laptop_prices(effective_date);

-- ===== TRIGGERS FOR UPDATED_AT =====

-- Laptop prices trigger
CREATE TRIGGER update_laptop_prices_updated_at
    BEFORE UPDATE ON laptop_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
