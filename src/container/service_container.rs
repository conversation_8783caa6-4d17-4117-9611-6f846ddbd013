use crate::{
    config::Config,
    container::service_container_traits::ServiceRegistry,
    database::Database,
    modules::{
        auth::{
            oauth_providers::{GoogleOAuthProvider, OAuthProviderFactory},
            service::AuthService,
            service_trait::AuthServiceTrait,
            GoogleOAuthService,
        },
        email::EmailEventBus,
        permission::{service::PermissionService, service_trait::PermissionServiceTrait},
        progression::{
            exp_system::{
                repository::DieselExpSystemRepository, service::ExpSystemService,
                DynExpSystemService,
            },
            title_system::{
                repository::DieselTitleRepository,
                service::{DynTitleService, TitleService},
            },
        },
        redis::{RedisManager, RedisService, RedisServiceTrait},
        role::{service::RoleService, service_trait::RoleServiceTrait},
        user::{
            repository::DieselUserRepository, service::UserService,
            service_trait::UserServiceTrait, user_role_repository::DieselUserRoleRepository,
        },
    },
};
use std::sync::Arc;

/// Type alias for complex service tuple to reduce type complexity
type ServiceTuple = (
    Arc<dyn UserServiceTrait>,
    Arc<dyn AuthServiceTrait>,
    Arc<dyn PermissionServiceTrait>,
    Arc<dyn RoleServiceTrait>,
    DynExpSystemService,
    DynTitleService,
    Option<Arc<dyn RedisServiceTrait>>,
);

/// Dependency Injection Container (implements DIP through ServiceRegistry trait)
#[derive(Clone)]
pub struct ServiceContainer {
    user_service: Arc<dyn UserServiceTrait>,
    auth_service: Arc<dyn AuthServiceTrait>,
    permission_service: Arc<dyn PermissionServiceTrait>,
    role_service: Arc<dyn RoleServiceTrait>,
    exp_system_service: DynExpSystemService,
    title_service: DynTitleService,
    email_event_bus: Arc<EmailEventBus>,
    redis_service: Option<Arc<dyn RedisServiceTrait>>, // Make optional for graceful fallback
    database: Database,
    config: crate::config::Config,
}

impl ServiceContainer {
    /// Private helper to construct the service graph (DRY principle)
    fn build_services(
        database: &Database,
        config: &Config,
        email_event_bus: Arc<EmailEventBus>,
    ) -> ServiceTuple {
        // --- Redis Service Setup ---
        // Initialize Redis service as None initially, will be connected in background
        let redis_service: Option<Arc<dyn RedisServiceTrait>> = None;

        // Repository layer
        let user_repo = Arc::new(DieselUserRepository::new(database.clone()));
        let user_role_repo = Arc::new(DieselUserRoleRepository::new(database.clone()));
        let exp_system_repo = Arc::new(DieselExpSystemRepository::new(database.clone()));
        let title_repo = Arc::new(DieselTitleRepository::new(database.clone()));

        // Service layer
        let permission_service = Arc::new(PermissionService::new(database.clone()));
        let role_service = Arc::new(RoleService::new(
            database.clone(),
            permission_service.clone(),
            redis_service.clone(),
        ));
        let title_service: DynTitleService = Arc::new(TitleService::new(title_repo));

        let exp_system_service = Arc::new(ExpSystemService::new(
            exp_system_repo,
            user_repo.clone(),
            title_service.clone(),
        ));

        let user_service_impl = UserService::new(
            user_repo.clone(),
            user_role_repo,
            role_service.clone(),
            exp_system_service.clone(),
            database.clone(),
            redis_service.clone(),
            config.cache.clone(),
        );
        let user_service: Arc<dyn UserServiceTrait> = Arc::new(user_service_impl);

        // Spawn background task to connect Redis without blocking startup
        let redis_config = config.redis.clone();
        tokio::spawn(async move {
            tracing::info!("Starting Redis connection in background...");

            match tokio::time::timeout(
                std::time::Duration::from_secs(10), // 10 second timeout
                RedisManager::new(&redis_config),
            )
            .await
            {
                Ok(Ok(_redis_manager)) => {
                    tracing::info!("Redis connection established successfully in background");
                    // TODO: Update service container with Redis service when ready
                }
                Ok(Err(e)) => {
                    tracing::warn!(
                        "Failed to create Redis connection: {}. Running without Redis.",
                        e
                    );
                }
                Err(_) => {
                    tracing::warn!("Redis connection timeout. Running without Redis.");
                }
            }
        });

        // --- OAuth Provider Setup ---
        let mut oauth_provider_factory = OAuthProviderFactory::new();

        // Create and register Google OAuth provider if configured
        let google_oauth_service = match GoogleOAuthService::new(config.google_oauth.clone()) {
            Ok(service) => Arc::new(service),
            Err(e) => {
                tracing::error!("Failed to create GoogleOAuthService: {}", e);
                // Return an error from this function instead of panicking
                // This requires changing the function signature to return a Result
                panic!("Failed to create GoogleOAuthService: {}. Please check your OAuth configuration.", e);
            }
        };
        let google_provider = Arc::new(GoogleOAuthProvider::new(google_oauth_service));
        oauth_provider_factory.register_provider(google_provider);

        // You can add other providers here (e.g., Facebook, GitHub) in the same way

        let mut auth_service_impl = AuthService::with_oauth_provider_factory(
            user_service.clone(),
            user_repo,
            role_service.clone(),
            permission_service.clone(),
            config.jwt_secret.clone(),
            config.tokens.clone(),
            oauth_provider_factory,
        );

        auth_service_impl.set_email_event_bus(email_event_bus);
        let auth_service = Arc::new(auth_service_impl);

        (
            user_service,
            auth_service,
            permission_service,
            role_service,
            exp_system_service,
            title_service,
            redis_service,
        )
    }

    pub fn new(database: Database, config: Config) -> Self {
        // Create email event bus with a placeholder - will be updated when Redis is ready
        let email_event_bus = Arc::new(EmailEventBus::new_fallback());
        Self::with_email_event_bus(database, config, email_event_bus)
    }

    pub fn with_email_event_bus(
        database: Database,
        config: Config,
        email_event_bus: Arc<EmailEventBus>,
    ) -> Self {
        let (
            user_service,
            auth_service,
            permission_service,
            role_service,
            exp_system_service,
            title_service,
            redis_service,
        ) = Self::build_services(&database, &config, email_event_bus.clone());

        Self {
            user_service,
            auth_service,
            permission_service,
            role_service,
            exp_system_service,
            title_service,
            email_event_bus,
            redis_service,
            database,
            config,
        }
    }

    pub fn config(&self) -> &crate::config::Config {
        &self.config
    }
}

// Implement ServiceRegistry trait (DIP: Depend on abstractions)
impl ServiceRegistry for ServiceContainer {
    fn get_user_service(&self) -> Arc<dyn UserServiceTrait> {
        Arc::clone(&self.user_service) // Explicit Arc::clone
    }

    fn get_auth_service(&self) -> Arc<dyn AuthServiceTrait> {
        Arc::clone(&self.auth_service) // Explicit Arc::clone
    }

    fn get_permission_service(&self) -> Arc<dyn PermissionServiceTrait> {
        Arc::clone(&self.permission_service) // Explicit Arc::clone
    }

    fn get_role_service(&self) -> Arc<dyn RoleServiceTrait> {
        Arc::clone(&self.role_service) // Explicit Arc::clone
    }

    fn get_redis_service(&self) -> Option<Arc<dyn RedisServiceTrait>> {
        self.redis_service.clone() // Explicit Arc::clone
    }
}

// Convenience methods for direct access (consistent with trait interface)
impl ServiceContainer {
    pub fn user_service(&self) -> Arc<dyn UserServiceTrait> {
        Arc::clone(&self.user_service) // Explicit Arc::clone
    }

    pub fn auth_service(&self) -> Arc<dyn AuthServiceTrait> {
        Arc::clone(&self.auth_service) // Explicit Arc::clone
    }

    pub fn permission_service(&self) -> Arc<dyn PermissionServiceTrait> {
        Arc::clone(&self.permission_service) // Explicit Arc::clone
    }

    pub fn role_service(&self) -> Arc<dyn RoleServiceTrait> {
        Arc::clone(&self.role_service) // Explicit Arc::clone
    }

    pub fn redis_service(&self) -> Option<Arc<dyn RedisServiceTrait>> {
        self.redis_service.clone() // Explicit Arc::clone
    }

    pub fn exp_system_service(&self) -> DynExpSystemService {
        self.exp_system_service.clone()
    }

    pub fn title_service(&self) -> DynTitleService {
        self.title_service.clone()
    }

    pub fn email_event_bus(&self) -> Arc<EmailEventBus> {
        Arc::clone(&self.email_event_bus)
    }

    pub fn database(&self) -> &Database {
        &self.database
    }

    /// Initialize Redis service lazily
    pub async fn init_redis_service(&mut self, config: &Config) -> anyhow::Result<()> {
        if self.redis_service.is_some() {
            return Ok(()); // Already initialized
        }

        tracing::info!("Initializing Redis service...");
        match RedisManager::new(&config.redis).await {
            Ok(redis_manager) => {
                self.redis_service = Some(Arc::new(RedisService::new(redis_manager)));
                tracing::info!("Redis service initialized successfully");
                Ok(())
            }
            Err(e) => {
                tracing::error!("Failed to initialize Redis service: {}", e);
                Err(e)
            }
        }
    }

    /// Get Redis service status
    pub fn redis_status(&self) -> &str {
        match &self.redis_service {
            Some(_) => "Connected",
            None => "Not Connected",
        }
    }

    /// Check if Redis is available
    pub fn is_redis_available(&self) -> bool {
        self.redis_service.is_some()
    }
}
